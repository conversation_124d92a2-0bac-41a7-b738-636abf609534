import asyncio

from services.data_extraction.data_extractor import DataExtractor

tenant_id = "25b1a56a-3063-48f7-b250-cb19ba3241a8"
app_id = "e87edb52-2726-4396-9fdf-0b814ce9fe66"

extractor = DataExtractor(tenant_id, app_id)
# file_path = "/home/<USER>/Downloads/amazon_image.jpg"
file_path = "/home/<USER>/Downloads/test.wav"
# file_path = "/home/<USER>/Downloads/Amazon1.pdf"
res = asyncio.run(extractor.extract(file_path, "cqb84jime8pv8race12g"))
