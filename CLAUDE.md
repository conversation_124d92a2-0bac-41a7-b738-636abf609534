# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a FastAPI-based AI Assistant service that provides multiple AI-powered capabilities using Google's Generative AI and Vertex AI. The service is designed as a multi-tenant system with modular service architecture.

## Development Commands

### Running Tests
```bash
# Run all tests
python -m pytest tests/

# Run specific test file
python -m pytest tests/api/test_data_extractor.py

# Run with coverage
coverage run -m pytest tests/
coverage report -i

# Run tests using CI script
./ci-scripts/unittest.sh
```

### Running the Application
```bash
# Start the server
python app/main.py

# Or use the startup script
./scripts/start_server.sh
```

### Installing Dependencies
```bash
pip install -r requirements.txt
pip install -r requirements-dev.txt  # for development
```

## Architecture Overview

### Service Structure Pattern
Each service under `/app/services/` follows this structure:
- `router.py` - FastAPI endpoints
- `service_name.py` - Core business logic
- `type.py` - Pydantic models for request/response
- `constants.py` - Service-specific constants
- `helper.py` - Utility functions
- `prompt.md` - LLM prompts (for AI services)

### Request Flow
1. Request arrives at FastAPI router with headers: `X-Jiffy-Tenant-Id`, `X-Jiffy-App-Id`, `X-Jiffy-User-Id`
2. Middleware extracts headers and creates workspace context
3. Router validates payload using Pydantic models
4. Service class is instantiated with configuration
5. Business logic executes (may involve LLM calls)
6. Response returned in standard format: `{"status": bool, "data": dict, "error": str}`

### Key Configuration Classes
- `Settings` (app/core/config.py) - Basic app settings, Google Cloud credentials
- `ServicesProperties` - External service URLs from properties files
- `AuthProperties` - Authentication credentials from secrets store

### External Service Integration
- **JiffyDrive**: File storage - files are downloaded using `download_file_from_jiffy_drive()`
- **Authentication**: Token generation via `get_token()` using service accounts
- **Google AI**: Access through `google.generativeai` and `vertexai` libraries

### Adding New Services
1. Create new directory under `/app/services/your_service/`
2. Implement router.py following the pattern in existing services
3. Add service router to `app/main.py`
4. Create corresponding test file in `/tests/api/`

### Testing Guidelines
- Use pytest fixtures from `tests/conftest.py`
- Mock external services using `pytest-mock`
- Test files should mirror the service structure
- Always test both success and error cases

### Common Pitfalls to Avoid
- Always validate required headers (tenant/app IDs)
- Handle JiffyDrive file downloads before processing
- Use async/await consistently throughout the codebase
- Return errors in the standard response format
- Check for existing utilities before creating new ones