import asyncio
import json
import sys
from pathlib import Path
import os
import traceback
from datetime import datetime
# os.environ["API_QUERY_AGENT_ENV"] = "WS_PE_TRIAD_DEV"
# os.environ["API_QUERY_AGENT_ENV"] = "WS_PE_FP_UAT"
os.environ["API_QUERY_AGENT_ENV"] = "WS_TRIAD_UAT"

# Add project root to sys.path
# This allows importing modules from the 'app' directory
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# from app.services.api_query_agent.api_agent import ApiAgent
from app.services.new_query_agent.api_agent import ApiAgent

from app.services.gen_ai_service.gen_ai import GenAI
from app.common.type import ModelName
from typing import List

async def evaluate_response_with_llm(actual_output: dict, expected_output: dict) -> tuple[bool, str]:
    """
    Use Gemini Flash model to evaluate if actual output matches expected output.
    
    Args:
        actual_output: The actual response from the ApiAgent
        expected_output: The expected systemOutput from sessions.json

    Returns:
        tuple: (success: bool, reason: str) - Whether the outputs match and evaluation details
    """
    # Prepare the evaluation prompt
    evaluation_prompt = f"""
    You are an AI assistant evaluating test results. Compare the actual output with the expected output and determine if they match semantically.
    
    Expected Output:
    {json.dumps(expected_output, indent=2)}
    
    Actual Output:
    {json.dumps(actual_output, indent=2)}
    
    Analyze whether the actual output appropriately responds to the user query based on the expected output .
    Consider:
    1. Does the actual output contain the same type of information as expected?
    2. Is the response format similar (text, table, chart, etc.)?
    3. Are key data points or concepts present in both. They do not need to match exactly.
    4. Ignore mismatches in followup questions or actual number mismatches in the answers. Just look for similar type of responses as mentioned in the expected output
    5. Ignore complexity difference in the responses. Make sure similar info is present in the actual response like present in expected output.
    6. If the actual output says unable to retrieve or failed to get, consider as a failure
    
    Respond with a JSON object containing:
    - "match": boolean (true if outputs match semantically, false otherwise)
    - "reason": string (brief explanation of your evaluation)
    - "confidence": number (0-1, your confidence in the evaluation)
    """
    
    # Define output schema for structured response
    output_schema = {
        "type": "object",
        "properties": {
            "match": {"type": "boolean"},
            "reason": {"type": "string"},
            "confidence": {"type": "number"}
        },
        "required": ["match", "reason", "confidence"]
    }
    
    try:
        # Initialize GenAI with Gemini Flash model
        evaluator = GenAI(
            instruction=evaluation_prompt,
            output_schema=output_schema,
            model_name=ModelName.GEMINI_25_PRO,
            temperature=0  # Low temperature for consistent evaluation
        )
        
        # Get evaluation result
        result = await evaluator.process()
        
        if result["status"] and result["data"]:
            evaluation = result["data"]["output"]
            return evaluation["match"], evaluation["reason"]
        else:
            return False, f"LLM evaluation failed: {result.get('error', 'Unknown error')}"
            
    except Exception as e:
        return False, f"Error during LLM evaluation: {str(e)}"

async def run_test(session: dict, env_config: dict) -> List[dict]:
    """
    Runs regression tests for a single session.

    Args:
        session: A session dictionary containing conversations
        env_config: Environment configuration with account numbers

    Returns:
        A list of test results for each conversation in the session
    """
    results = []
    session_id = session.get("sessionId")
    conversations = session.get("conversations", [])

    if len(conversations) == 1:
        conv_details = conversations[0]
        user_input = conv_details.get("userInput")
        expected_output = conv_details.get("systemOutput")
        if not user_input:
            results.append({
                "sessionId": session_id,
                "query": user_input,
                "status": "skipped",
                "reason": "No user input provided"
            })
        user_input = user_input.replace("ACTNUM", env_config.get("actnum", 0))
        print(f"Running test for Session {session_id}, Conversation 0: {user_input}")

        try:
            # Initialize context with environment-specific data if needed
            context_data = {}
            # You might want to use env_config here if needed for account numbers

            agent = ApiAgent(context_data=context_data)
            actual_output = await agent.run(user_input)

            # Use LLM to evaluate the response
            match_success, reason = await evaluate_response_with_llm(actual_output, expected_output)

            match_status = "failed"
            if match_success:
                match_status = "passed"

            results.append({
                "sessionId": session_id,
                "conversationIndex": 0,
                "query": user_input,
                "status": match_status,
                "reason": reason,
                "actualOutput": actual_output,
                "expectedOutput": expected_output

            })

        except Exception as e:
            print(f"Error running test for query '{user_input}': {e}")
            results.append({
                "sessionId": session_id,
                "conversationIndex": 0,
                "query": user_input,
                "status": "error",
                "reason": str(e),
                "traceback": traceback.format_exc()
            })

    elif len(conversations) > 1:
        prev_query = ""
        prev_answer = ""
        for idx, conv_details in enumerate(conversations):
            user_input = conv_details.get("userInput")
            expected_output = conv_details.get("systemOutput")
            if not user_input:
                results.append({
                    "sessionId": session_id,
                    "query": user_input,
                    "status": "skipped",
                    "reason": "No user input provided"
                })
            user_input = user_input.replace("ACTNUM", env_config.get("actnum", 0))
            print(f"Running test for Session {session_id}, Conversation {idx}: {user_input}")
            try:
                # Initialize context with environment-specific data if needed
                context_data = {}
                if prev_query:
                    conv_history = [
                        {"actorType": "Human", "message": prev_query},
                        {"actorType": "Machine", "message": prev_answer},
                        {"actorType": "Human", "message": user_input},
                    ]
                    context_data = conv_history
                agent = ApiAgent(context_data=context_data)
                actual_output = await agent.run(user_input)
                prev_query = user_input
                prev_answer = actual_output

                # Use LLM to evaluate the response
                match_success, reason = await evaluate_response_with_llm(actual_output, expected_output)

                match_status = "failed"
                if match_success:
                    match_status = "passed"

                results.append({
                    "sessionId": session_id,
                    "conversationIndex": idx,
                    "query": user_input,
                    "status": match_status,
                    "reason": reason,
                    "actualOutput": actual_output,
                    "expectedOutput": expected_output

                })

            except Exception as e:
                print(f"Error running test for query '{user_input}': {e}")
                results.append({
                    "sessionId": session_id,
                    "conversationIndex": idx,
                    "query": user_input,
                    "status": "error",
                    "reason": str(e),
                    "traceback": traceback.format_exc()
                })
    return results


async def main():
    """
    Main function to load session data and run all regression tests.
    """
    # sessions_path = Path(__file__).parent / "sessions" / "sessions.json"
    # sessions_path = Path(__file__).parent / "sessions" / "sessions_qa.json"
    sessions_path = Path(__file__).parent / "sessions" / "sessions_consolidated.json"

    # sessions_path = Path(__file__).parent / "sessions" / "sessions_debug.json"

    try:
        with open(sessions_path, 'r') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"Error: Sessions file not found at {sessions_path}")
        return
    except json.JSONDecodeError:
        print(f"Error: Could not decode JSON from {sessions_path}")
        return

    env_config = data.get("env", {})
    sessions = data.get("session", [])
    
    if not sessions:
        print("No sessions found in the data file.")
        return

    # Get current environment
    current_env = os.environ.get("API_QUERY_AGENT_ENV", "WS_PE_TRIAD_DEV")
    env_data = env_config.get(current_env, {})
    print(f"Using environment: {current_env}")
    if env_data:
        print(f"Environment config: {env_data}")
    
    # Process sessions with concurrency limit
    semaphore = asyncio.Semaphore(10)  # Reduced concurrency for LLM calls
    
    async def run_with_semaphore(session):
        async with semaphore:
            try:
                return await asyncio.wait_for(run_test(session, env_data), timeout=60)
            except asyncio.TimeoutError:
                print(f"Task {session} timed out.")
                return []
            except Exception as e:
                print(f"Task {session} failed with error: {e}")
                return []
    
    # Run all sessions
    tasks = [run_with_semaphore(session) for session in sessions]
    all_results = await asyncio.gather(*tasks)
    
    # Flatten results from all sessions
    results = [item for sublist in all_results for item in sublist]
    
    # Count results
    passed_count = sum(1 for r in results if r['status'] == 'passed')
    failed_count = sum(1 for r in results if r['status'] == 'failed')
    skipped_count = sum(1 for r in results if r['status'] == 'skipped')
    error_count = sum(1 for r in results if r['status'] == 'error')
    
    # Print detailed results
    print("\n--- Test Results ---")
    for result in results:
        print(f"Session {result['sessionId']}, Conversation {result['conversationIndex']}")
        print(f"Query: {result['query']}")
        print(f"Status: {result['status'].upper()}")
        print(f"Reason: {result.get('reason', 'N/A')}")
        
        if result['status'] == 'failed':
            print(f"Expected Output: {json.dumps(result.get('expectedOutput'), indent=2)}")
            print(f"Actual Output: {json.dumps(result.get('actualOutput'), indent=2)}")
        
        if 'traceback' in result:
            print(f"Traceback: {result['traceback']}")
        
        print("-" * 50)
    
    # Print summary
    print("\n--- Summary ---")
    print(f"Total tests: {len(results)}")
    print(f"Passed: {passed_count}")
    print(f"Failed: {failed_count}")
    print(f"Skipped: {skipped_count}")
    print(f"Errors: {error_count}")
    print("-----------------")
    
    # Save detailed results to file
    # results_file = Path(__file__).parent / "regression_test_results_trial_FULL.json"
    # results_file = Path(__file__).parent / "regression_test_results_trial_QA_FULL.json"
    results_file = Path(__file__).parent / "regression_test_results_triad_CONSOLIDATED.json"

    with open(results_file, 'w') as f:
        json.dump({
            "environment": current_env,
            "summary": {
                "total": len(results),
                "passed": passed_count,
                "failed": failed_count,
                "skipped": skipped_count,
                "errors": error_count
            },
            "results": results
        }, f, indent=2)
    print(f"\nDetailed results saved to: {results_file}")
    
    # Save failed test cases to error_information.json
    failed_tests = [r for r in results if r['status'] in ['failed', 'error']]
    if failed_tests:
        error_info_file = Path(__file__).parent / "error_information.json"
        error_data = {
            "environment": current_env,
            "timestamp": datetime.now().isoformat(),
            "total_failed": len(failed_tests),
            "failed_tests": []
        }
        
        for test in failed_tests:
            error_entry = {
                "sessionId": test.get("sessionId"),
                "conversationIndex": test.get("conversationIndex"),
                "question": test.get("query"),
                "status": test.get("status"),
                "error_reason": test.get("reason"),
                "execution_logs": {
                    "expected_output": test.get("expectedOutput"),
                    "actual_output": test.get("actualOutput"),
                    "traceback": test.get("traceback", "No traceback available")
                }
            }
            error_data["failed_tests"].append(error_entry)
        
        with open(error_info_file, 'w') as f:
            json.dump(error_data, f, indent=2)
        print(f"\nError information saved to: {error_info_file}")
    
    if failed_count > 0 or error_count > 0:
        sys.exit(1)


if __name__ == "__main__":
    # To run this script, execute `python tests/data/api_query_agent/chat_agent_regression_tests.py` from the project root.
    asyncio.run(main())
